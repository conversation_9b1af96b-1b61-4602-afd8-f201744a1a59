{"name": "monad-faas-demo", "version": "1.0.0", "description": "MonadFaas Scalability Demo - 100 parallel function executions", "main": "demo-script.js", "scripts": {"demo": "node demo-script.js", "demo:small": "node demo-script.js --functions=10", "demo:large": "node demo-script.js --functions=150", "verify": "cast call ****************************************** \"nextFunctionId()\" --rpc-url http://localhost:8545"}, "dependencies": {"ethers": "^6.14.3", "chalk": "^4.1.2"}, "keywords": ["blockchain", "serverless", "faas", "monad", "scalability", "demo"], "author": "MonadBot", "license": "MIT"}