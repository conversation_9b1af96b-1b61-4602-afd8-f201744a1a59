{"id": "72cc0e021fa49a80", "source_id_to_path": {"0": "lib/forge-std/src/Base.sol", "1": "lib/forge-std/src/Script.sol", "2": "lib/forge-std/src/StdChains.sol", "3": "lib/forge-std/src/StdCheats.sol", "4": "lib/forge-std/src/StdConstants.sol", "5": "lib/forge-std/src/StdJson.sol", "6": "lib/forge-std/src/StdMath.sol", "7": "lib/forge-std/src/StdStorage.sol", "8": "lib/forge-std/src/StdStyle.sol", "9": "lib/forge-std/src/StdUtils.sol", "10": "lib/forge-std/src/Vm.sol", "11": "lib/forge-std/src/console.sol", "12": "lib/forge-std/src/console2.sol", "13": "lib/forge-std/src/interfaces/IMulticall3.sol", "14": "lib/forge-std/src/safeconsole.sol", "15": "lib/openzeppelin-contracts/contracts/access/AccessControl.sol", "16": "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol", "17": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "18": "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol", "19": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "20": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "21": "script/DeployOptimizedRegistry.s.sol", "22": "src/OptimizedFunctionRegistry.sol"}, "language": "Solidity"}