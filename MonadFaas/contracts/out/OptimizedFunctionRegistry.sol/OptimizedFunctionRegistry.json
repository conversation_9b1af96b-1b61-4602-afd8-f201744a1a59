{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEVELOPER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addTrigger", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchRegisterFunctions", "inputs": [{"name": "names", "type": "string[]", "internalType": "string[]"}, {"name": "descriptions", "type": "string[]", "internalType": "string[]"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "gasLimits", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "runtimes", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "functionIds", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "executionHistory", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "timestamp", "type": "uint64", "internalType": "uint64"}, {"name": "gasUsed", "type": "uint32", "internalType": "uint32"}, {"name": "success", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "fireTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "functions", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "createdAt", "type": "uint64", "internalType": "uint64"}, {"name": "executionCount", "type": "uint64", "internalType": "uint64"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "runtime", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getExecutionCount", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getFunction", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct OptimizedFunctionRegistry.FunctionMetadata", "components": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "createdAt", "type": "uint64", "internalType": "uint64"}, {"name": "executionCount", "type": "uint64", "internalType": "uint64"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "runtime", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct OptimizedFunctionRegistry.TriggerRule", "components": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "lastTriggered", "type": "uint64", "internalType": "uint64"}, {"name": "triggerCount", "type": "uint64", "internalType": "uint64"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isActive", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "nextFunctionId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nextTriggerId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "registerFunction", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "runtime", "type": "string", "internalType": "string"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reportExecution", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "success", "type": "bool", "internalType": "bool"}, {"name": "", "type": "bytes", "internalType": "bytes"}, {"name": "gasUsed", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "triggers", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "lastTriggered", "type": "uint64", "internalType": "uint64"}, {"name": "triggerCount", "type": "uint64", "internalType": "uint64"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "event", "name": "FunctionExecuted", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "success", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "FunctionRegistered", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wasmHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TriggerAdded", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TriggerFired", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "FunctionInactive", "inputs": []}, {"type": "error", "name": "FunctionNotFound", "inputs": []}, {"type": "error", "name": "GasLimitExceeded", "inputs": []}, {"type": "error", "name": "InvalidWasmHash", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "TriggerInactive", "inputs": []}, {"type": "error", "name": "TriggerNotFound", "inputs": []}, {"type": "error", "name": "UnauthorizedAccess", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "293:9174:22:-:0;;;2369:1;2337:33;;2407:1;2376:32;;3278:165;;;;;;;;;;1857:1:18;2061:7;:21;;;;3302:42:22;2232:4:15;3313:18:22;;3333:10;3302;;;:42;;:::i;:::-;;3354:34;405:23;3377:10;3354;;;:34;;:::i;:::-;;3398:38;475:27;3425:10;3398;;;:38;;:::i;:::-;;293:9174;;6179:316:15;6256:4;6277:22;6285:4;6291:7;6277;;;:22;;:::i;:::-;6272:217;;6347:4;6315:6;:12;6322:4;6315:12;;;;;;;;;;;:20;;:29;6336:7;6315:29;;;;;;;;;;;;;;;;:36;;;;;;;;;;;;;;;;;;6397:12;:10;;;:12;;:::i;:::-;6370:40;;6388:7;6370:40;;6382:4;6370:40;;;;;;;;;;6431:4;6424:11;;;;6272:217;6473:5;6466:12;;6179:316;;;;;:::o;2854:136::-;2931:4;2954:6;:12;2961:4;2954:12;;;;;;;;;;;:20;;:29;2975:7;2954:29;;;;;;;;;;;;;;;;;;;;;;;;;2947:36;;2854:136;;;;:::o;656:96:17:-;709:7;735:10;728:17;;656:96;:::o;293:9174:22:-;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "293:9174:22:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2265:61;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;;2565:202:15;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;9066:170:22;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2337:33;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;8601:166;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3810:120:15;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4226:136;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;5328:245;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2376:32:22;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;5406:694;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;7029:1500;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4498:843;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;368:60;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;9314:151;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;434:68;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2854:136:15;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;8834:157:22;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2187:49:15;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2212:47:22;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;:::i;:::-;;;;;;;;6176:775;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;3528:901;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;4642:138:15;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;2414:49:22;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;2153:53;;;;;;;;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;2265:61;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;2565:202:15:-;2650:4;2688:32;2673:47;;;:11;:47;;;;:87;;;;2724:36;2748:11;2724:23;:36::i;:::-;2673:87;2666:94;;2565:202;;;:::o;9066:170:22:-;9163:6;9193:9;:21;9203:10;9193:21;;;;;;;;;;;:36;;;;;;;;;;;;9186:43;;9066:170;;;:::o;2337:33::-;;;;:::o;8601:166::-;8692:23;;:::i;:::-;8739:9;:21;8749:10;8739:21;;;;;;;;;;;8732:28;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8601:166;;;:::o;3810:120:15:-;3875:7;3901:6;:12;3908:4;3901:12;;;;;;;;;;;:22;;;3894:29;;3810:120;;;:::o;4226:136::-;4300:18;4313:4;4300:12;:18::i;:::-;2464:16;2475:4;2464:10;:16::i;:::-;4330:25:::1;4341:4;4347:7;4330:10;:25::i;:::-;;4226:136:::0;;;:::o;5328:245::-;5443:12;:10;:12::i;:::-;5421:34;;:18;:34;;;5417:102;;5478:30;;;;;;;;;;;;;;5417:102;5529:37;5541:4;5547:18;5529:11;:37::i;:::-;;5328:245;;:::o;2376:32:22:-;;;;:::o;5406:694::-;405:23;2464:16:15;2475:4;2464:10;:16::i;:::-;5542:27:22::1;5572:8;:19;5581:9;5572:19;;;;;;;;;;;5542:49;;5627:1;5605:7;:18;;;:23:::0;5601:53:::1;;5637:17;;;;;;;;;;;;;;5601:53;5669:7;:14;;;;;;;;;;;;5664:45;;5692:17;;;;;;;;;;;;;;5664:45;5720:29;5752:9;:29;5762:7;:18;;;5752:29;;;;;;;;;;;5720:61;;5796:4;:11;;;;;;;;;;;;5791:43;;5816:18;;;;;;;;;;;;;;5791:43;5972:15;5941:7;:21;;;:47;;;;;;;;;;;;;;;;;;6002:7;:20;;;:22;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6074:7;:18;;;6063:9;6050:43;;;;;;;;;;5532:568;;5406:694:::0;;;;:::o;7029:1500::-;7270:28;7310:14;7327:5;;:12;;7310:29;;7367:12;;:19;;7357:6;:29;:60;;;;;7400:10;;:17;;7390:6;:27;7357:60;:106;;;;;7447:9;;:16;;7437:6;:26;7357:106;:135;;;;;7477:8;;:15;;7467:6;:25;7357:135;7349:169;;;;;;;;;;;;:::i;:::-;;;;;;;;;7557:6;7543:21;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7529:35;;7580:9;7592:1;7580:13;;7575:948;7599:6;7595:1;:10;7575:948;;;7714:1;7706:10;;7689;;7700:1;7689:13;;;;;;;:::i;:::-;;;;;;;;:27;7685:57;;7725:17;;;;;;;;;;;;;;7685:57;2454:9;7760;;7770:1;7760:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;:28;;;7756:59;;;7797:18;;;;;;;;;;;;;;7756:59;7830:18;7851:14;;:16;;;;;;;;;:::i;:::-;;;;;7830:37;;7898:10;7881:11;7893:1;7881:14;;;;;;;;:::i;:::-;;;;;;;:27;;;;;7983:29;8015:9;:21;8025:10;8015:21;;;;;;;;;;;7983:53;;8066:10;;8077:1;8066:13;;;;;;;:::i;:::-;;;;;;;;8050:4;:13;;:29;;;;8105:5;;8111:1;8105:8;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;8093:4;:9;;:20;;;;;;;:::i;:::-;;8146:12;;8159:1;8146:15;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;8127:4;:16;;:34;;;;;;;:::i;:::-;;8188:10;8175:4;:10;;;:23;;;;;;;;;;;;;;;;;;8228:9;;8238:1;8228:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;;:::i;:::-;8212:4;:13;;;:28;;;;;;;;;;;;;;;;;;8268:4;8254;:11;;;:18;;;;;;;;;;;;;;;;;;8310:15;8286:4;:14;;;:40;;;;;;;;;;;;;;;;;;8362:1;8340:4;:19;;;:23;;;;;;;;;;;;;;;;;;8392:8;;8401:1;8392:11;;;;;;;:::i;:::-;;;;;;;;;;;;;:::i;:::-;8377:4;:12;;:26;;;;;;;:::i;:::-;;8454:10;8423:57;;8442:10;8423:57;8466:10;;8477:1;8466:13;;;;;;;:::i;:::-;;;;;;;;8423:57;;;;;;:::i;:::-;;;;;;;;8507:3;;;;;7608:915;;7575:948;;;;7300:1229;7029:1500;;;;;;;;;;;;:::o;4498:843::-;4639:17;4668:29;4700:9;:21;4710:10;4700:21;;;;;;;;;;;4668:53;;4757:1;4735:24;;:4;:10;;;;;;;;;;;;:24;;;4731:55;;4768:18;;;;;;;;;;;;;;4731:55;4814:10;4800:24;;:4;:10;;;;;;;;;;;;:24;;;;:60;;;;;4829:31;405:23;4849:10;4829:7;:31::i;:::-;4828:32;4800:60;4796:118;;;4883:20;;;;;;;;;;;;;;4796:118;4936:13;;:15;;;;;;;;;:::i;:::-;;;;;4924:27;;5007;5037:8;:19;5046:9;5037:19;;;;;;;;;;;5007:49;;5087:10;5066:7;:18;;:31;;;;5129:11;5107:7;:19;;;:33;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;5172:11;;5150:7;:19;;:33;;;;;;;:::i;:::-;;5210:4;5193:7;:14;;;:21;;;;;;;;;;;;;;;;;;5248:1;5224:7;:21;;;:25;;;;;;;;;;;;;;;;;;5282:1;5259:7;:20;;;:24;;;;;;;;;;;;;;;;;;5323:10;5312:9;5299:35;;;;;;;;;;4658:683;;4498:843;;;;;;:::o;368:60::-;405:23;368:60;:::o;9314:151::-;9402:4;9430:9;:21;9440:10;9430:21;;;;;;;;;;;:28;;;;;;;;;;;;9423:35;;9314:151;;;:::o;434:68::-;475:27;434:68;:::o;2854:136:15:-;2931:4;2954:6;:12;2961:4;2954:12;;;;;;;;;;;:20;;:29;2975:7;2954:29;;;;;;;;;;;;;;;;;;;;;;;;;2947:36;;2854:136;;;;:::o;8834:157:22:-;8923:18;;:::i;:::-;8965:8;:19;8974:9;8965:19;;;;;;;;;;;8958:26;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8834:157;;;:::o;2187:49:15:-;2232:4;2187:49;;;:::o;2212:47:22:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;6176:775::-;405:23;2464:16:15;2475:4;2464:10;:16::i;:::-;6433:29:22::1;6465:9;:21;6475:10;6465:21;;;;;;;;;;;6433:53;;6522:1;6500:24;;:4;:10;;;;;;;;;;;;:24;;::::0;6496:55:::1;;6533:18;;;;;;;;;;;;;;6496:55;6628:4;:19;;;:21;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6710:16;:28;6727:10;6710:28;;;;;;;;;;;6744:135;;;;;;;;6792:15;6744:135;;;;;;6831:7;6744:135;;;;;;6861:7;6744:135;;;;::::0;6710:170:::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6925:9;6913:10;6896:48;6936:7;6896:48;;;;;;:::i;:::-;;;;;;;;6423:528;6176:775:::0;;;;;;;;;:::o;3528:901::-;3729:18;3783:1;3775:10;;3763:8;:22;3759:52;;3794:17;;;;;;;;;;;;;;3759:52;2454:9;3825:8;:24;;;3821:55;;;3858:18;;;;;;;;;;;;;;3821:55;3900:14;;:16;;;;;;;;;:::i;:::-;;;;;3887:29;;3991;4023:9;:21;4033:10;4023:21;;;;;;;;;;;3991:53;;4070:8;4054:4;:13;;:24;;;;4100:4;;4088;:9;;:16;;;;;;;:::i;:::-;;4133:11;;4114:4;:16;;:30;;;;;;;:::i;:::-;;4167:10;4154:4;:10;;;:23;;;;;;;;;;;;;;;;;;4203:8;4187:4;:13;;;:24;;;;;;;;;;;;;;;;;;4235:4;4221;:11;;;:18;;;;;;;;;;;;;;;;;;4273:15;4249:4;:14;;;:40;;;;;;;;;;;;;;;;;;4321:1;4299:4;:19;;;:23;;;;;;;;;;;;;;;;;;4347:7;;4332:4;:12;;:22;;;;;;;:::i;:::-;;4401:10;4370:52;;4389:10;4370:52;4413:8;4370:52;;;;;;:::i;:::-;;;;;;;;3749:680;3528:901;;;;;;;;;;:::o;4642:138:15:-;4717:18;4730:4;4717:12;:18::i;:::-;2464:16;2475:4;2464:10;:16::i;:::-;4747:26:::1;4759:4;4765:7;4747:11;:26::i;:::-;;4642:138:::0;;;:::o;2414:49:22:-;2454:9;2414:49;:::o;2153:53::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;763:146:19:-;839:4;877:25;862:40;;;:11;:40;;;;855:47;;763:146;;;:::o;3199:103:15:-;3265:30;3276:4;3282:12;:10;:12::i;:::-;3265:10;:30::i;:::-;3199:103;:::o;6179:316::-;6256:4;6277:22;6285:4;6291:7;6277;:22::i;:::-;6272:217;;6347:4;6315:6;:12;6322:4;6315:12;;;;;;;;;;;:20;;:29;6336:7;6315:29;;;;;;;;;;;;;;;;:36;;;;;;;;;;;;;;;;;;6397:12;:10;:12::i;:::-;6370:40;;6388:7;6370:40;;6382:4;6370:40;;;;;;;;;;6431:4;6424:11;;;;6272:217;6473:5;6466:12;;6179:316;;;;;:::o;656:96:17:-;709:7;735:10;728:17;;656:96;:::o;6732:317:15:-;6810:4;6830:22;6838:4;6844:7;6830;:22::i;:::-;6826:217;;;6900:5;6868:6;:12;6875:4;6868:12;;;;;;;;;;;:20;;:29;6889:7;6868:29;;;;;;;;;;;;;;;;:37;;;;;;;;;;;;;;;;;;6951:12;:10;:12::i;:::-;6924:40;;6942:7;6924:40;;6936:4;6924:40;;;;;;;;;;6985:4;6978:11;;;;6826:217;7027:5;7020:12;;6732:317;;;;;:::o;3432:197::-;3520:22;3528:4;3534:7;3520;:22::i;:::-;3515:108;;3598:7;3607:4;3565:47;;;;;;;;;;;;:::i;:::-;;;;;;;;3515:108;3432:197;;:::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;:::o;88:117:23:-;197:1;194;187:12;211:117;320:1;317;310:12;334:77;371:7;400:5;389:16;;334:77;;;:::o;417:122::-;490:24;508:5;490:24;:::i;:::-;483:5;480:35;470:63;;529:1;526;519:12;470:63;417:122;:::o;545:139::-;591:5;629:6;616:20;607:29;;645:33;672:5;645:33;:::i;:::-;545:139;;;;:::o;690:474::-;758:6;766;815:2;803:9;794:7;790:23;786:32;783:119;;;821:79;;:::i;:::-;783:119;941:1;966:53;1011:7;1002:6;991:9;987:22;966:53;:::i;:::-;956:63;;912:117;1068:2;1094:53;1139:7;1130:6;1119:9;1115:22;1094:53;:::i;:::-;1084:63;;1039:118;690:474;;;;;:::o;1170:101::-;1206:7;1246:18;1239:5;1235:30;1224:41;;1170:101;;;:::o;1277:115::-;1362:23;1379:5;1362:23;:::i;:::-;1357:3;1350:36;1277:115;;:::o;1398:93::-;1434:7;1474:10;1467:5;1463:22;1452:33;;1398:93;;;:::o;1497:115::-;1582:23;1599:5;1582:23;:::i;:::-;1577:3;1570:36;1497:115;;:::o;1618:90::-;1652:7;1695:5;1688:13;1681:21;1670:32;;1618:90;;;:::o;1714:109::-;1795:21;1810:5;1795:21;:::i;:::-;1790:3;1783:34;1714:109;;:::o;1829:422::-;1968:4;2006:2;1995:9;1991:18;1983:26;;2019:69;2085:1;2074:9;2070:17;2061:6;2019:69;:::i;:::-;2098:70;2164:2;2153:9;2149:18;2140:6;2098:70;:::i;:::-;2178:66;2240:2;2229:9;2225:18;2216:6;2178:66;:::i;:::-;1829:422;;;;;;:::o;2257:149::-;2293:7;2333:66;2326:5;2322:78;2311:89;;2257:149;;;:::o;2412:120::-;2484:23;2501:5;2484:23;:::i;:::-;2477:5;2474:34;2464:62;;2522:1;2519;2512:12;2464:62;2412:120;:::o;2538:137::-;2583:5;2621:6;2608:20;2599:29;;2637:32;2663:5;2637:32;:::i;:::-;2538:137;;;;:::o;2681:327::-;2739:6;2788:2;2776:9;2767:7;2763:23;2759:32;2756:119;;;2794:79;;:::i;:::-;2756:119;2914:1;2939:52;2983:7;2974:6;2963:9;2959:22;2939:52;:::i;:::-;2929:62;;2885:116;2681:327;;;;:::o;3014:210::-;3101:4;3139:2;3128:9;3124:18;3116:26;;3152:65;3214:1;3203:9;3199:17;3190:6;3152:65;:::i;:::-;3014:210;;;;:::o;3230:329::-;3289:6;3338:2;3326:9;3317:7;3313:23;3309:32;3306:119;;;3344:79;;:::i;:::-;3306:119;3464:1;3489:53;3534:7;3525:6;3514:9;3510:22;3489:53;:::i;:::-;3479:63;;3435:117;3230:329;;;;:::o;3565:218::-;3656:4;3694:2;3683:9;3679:18;3671:26;;3707:69;3773:1;3762:9;3758:17;3749:6;3707:69;:::i;:::-;3565:218;;;;:::o;3789:118::-;3876:24;3894:5;3876:24;:::i;:::-;3871:3;3864:37;3789:118;;:::o;3913:222::-;4006:4;4044:2;4033:9;4029:18;4021:26;;4057:71;4125:1;4114:9;4110:17;4101:6;4057:71;:::i;:::-;3913:222;;;;:::o;4141:77::-;4178:7;4207:5;4196:16;;4141:77;;;:::o;4224:108::-;4301:24;4319:5;4301:24;:::i;:::-;4296:3;4289:37;4224:108;;:::o;4338:126::-;4375:7;4415:42;4408:5;4404:54;4393:65;;4338:126;;;:::o;4470:96::-;4507:7;4536:24;4554:5;4536:24;:::i;:::-;4525:35;;4470:96;;;:::o;4572:108::-;4649:24;4667:5;4649:24;:::i;:::-;4644:3;4637:37;4572:108;;:::o;4686:109::-;4722:7;4762:26;4755:5;4751:38;4740:49;;4686:109;;;:::o;4801:105::-;4876:23;4893:5;4876:23;:::i;:::-;4871:3;4864:36;4801:105;;:::o;4912:::-;4987:23;5004:5;4987:23;:::i;:::-;4982:3;4975:36;4912:105;;:::o;5023:99::-;5094:21;5109:5;5094:21;:::i;:::-;5089:3;5082:34;5023:99;;:::o;5128:::-;5180:6;5214:5;5208:12;5198:22;;5128:99;;;:::o;5233:159::-;5307:11;5341:6;5336:3;5329:19;5381:4;5376:3;5372:14;5357:29;;5233:159;;;;:::o;5398:139::-;5487:6;5482:3;5477;5471:23;5528:1;5519:6;5514:3;5510:16;5503:27;5398:139;;;:::o;5543:102::-;5584:6;5635:2;5631:7;5626:2;5619:5;5615:14;5611:28;5601:38;;5543:102;;;:::o;5651:357::-;5729:3;5757:39;5790:5;5757:39;:::i;:::-;5812:61;5866:6;5861:3;5812:61;:::i;:::-;5805:68;;5882:65;5940:6;5935:3;5928:4;5921:5;5917:16;5882:65;:::i;:::-;5972:29;5994:6;5972:29;:::i;:::-;5967:3;5963:39;5956:46;;5733:275;5651:357;;;;:::o;6124:2016::-;6263:3;6299:6;6294:3;6290:16;6392:4;6385:5;6381:16;6375:23;6411:63;6468:4;6463:3;6459:14;6445:12;6411:63;:::i;:::-;6316:168;6567:4;6560:5;6556:16;6550:23;6586:63;6643:4;6638:3;6634:14;6620:12;6586:63;:::i;:::-;6494:165;6745:4;6738:5;6734:16;6728:23;6764:61;6819:4;6814:3;6810:14;6796:12;6764:61;:::i;:::-;6669:166;6922:4;6915:5;6911:16;6905:23;6941:61;6996:4;6991:3;6987:14;6973:12;6941:61;:::i;:::-;6845:167;7104:4;7097:5;7093:16;7087:23;7123:61;7178:4;7173:3;7169:14;7155:12;7123:61;:::i;:::-;7022:172;7278:4;7271:5;7267:16;7261:23;7297:57;7348:4;7343:3;7339:14;7325:12;7297:57;:::i;:::-;7204:160;7446:4;7439:5;7435:16;7429:23;7499:3;7493:4;7489:14;7482:4;7477:3;7473:14;7466:38;7525:73;7593:4;7579:12;7525:73;:::i;:::-;7517:81;;7374:235;7698:4;7691:5;7687:16;7681:23;7751:3;7745:4;7741:14;7734:4;7729:3;7725:14;7718:38;7777:73;7845:4;7831:12;7777:73;:::i;:::-;7769:81;;7619:242;7946:6;7939:5;7935:18;7929:25;8003:3;7997:4;7993:14;7984:6;7979:3;7975:16;7968:40;8029:73;8097:4;8083:12;8029:73;:::i;:::-;8021:81;;7871:242;8130:4;8123:11;;6268:1872;6124:2016;;;;:::o;8146:413::-;8309:4;8347:2;8336:9;8332:18;8324:26;;8396:9;8390:4;8386:20;8382:1;8371:9;8367:17;8360:47;8424:128;8547:4;8538:6;8424:128;:::i;:::-;8416:136;;8146:413;;;;:::o;8565:122::-;8638:24;8656:5;8638:24;:::i;:::-;8631:5;8628:35;8618:63;;8677:1;8674;8667:12;8618:63;8565:122;:::o;8693:139::-;8739:5;8777:6;8764:20;8755:29;;8793:33;8820:5;8793:33;:::i;:::-;8693:139;;;;:::o;8838:329::-;8897:6;8946:2;8934:9;8925:7;8921:23;8917:32;8914:119;;;8952:79;;:::i;:::-;8914:119;9072:1;9097:53;9142:7;9133:6;9122:9;9118:22;9097:53;:::i;:::-;9087:63;;9043:117;8838:329;;;;:::o;9173:118::-;9260:24;9278:5;9260:24;:::i;:::-;9255:3;9248:37;9173:118;;:::o;9297:222::-;9390:4;9428:2;9417:9;9413:18;9405:26;;9441:71;9509:1;9498:9;9494:17;9485:6;9441:71;:::i;:::-;9297:222;;;;:::o;9525:122::-;9598:24;9616:5;9598:24;:::i;:::-;9591:5;9588:35;9578:63;;9637:1;9634;9627:12;9578:63;9525:122;:::o;9653:139::-;9699:5;9737:6;9724:20;9715:29;;9753:33;9780:5;9753:33;:::i;:::-;9653:139;;;;:::o;9798:474::-;9866:6;9874;9923:2;9911:9;9902:7;9898:23;9894:32;9891:119;;;9929:79;;:::i;:::-;9891:119;10049:1;10074:53;10119:7;10110:6;10099:9;10095:22;10074:53;:::i;:::-;10064:63;;10020:117;10176:2;10202:53;10247:7;10238:6;10227:9;10223:22;10202:53;:::i;:::-;10192:63;;10147:118;9798:474;;;;;:::o;10278:117::-;10387:1;10384;10377:12;10401:117;10510:1;10507;10500:12;10524:117;10633:1;10630;10623:12;10660:552;10717:8;10727:6;10777:3;10770:4;10762:6;10758:17;10754:27;10744:122;;10785:79;;:::i;:::-;10744:122;10898:6;10885:20;10875:30;;10928:18;10920:6;10917:30;10914:117;;;10950:79;;:::i;:::-;10914:117;11064:4;11056:6;11052:17;11040:29;;11118:3;11110:4;11102:6;11098:17;11088:8;11084:32;11081:41;11078:128;;;11125:79;;:::i;:::-;11078:128;10660:552;;;;;:::o;11218:672::-;11297:6;11305;11313;11362:2;11350:9;11341:7;11337:23;11333:32;11330:119;;;11368:79;;:::i;:::-;11330:119;11488:1;11513:53;11558:7;11549:6;11538:9;11534:22;11513:53;:::i;:::-;11503:63;;11459:117;11643:2;11632:9;11628:18;11615:32;11674:18;11666:6;11663:30;11660:117;;;11696:79;;:::i;:::-;11660:117;11809:64;11865:7;11856:6;11845:9;11841:22;11809:64;:::i;:::-;11791:82;;;;11586:297;11218:672;;;;;:::o;11912:580::-;11997:8;12007:6;12057:3;12050:4;12042:6;12038:17;12034:27;12024:122;;12065:79;;:::i;:::-;12024:122;12178:6;12165:20;12155:30;;12208:18;12200:6;12197:30;12194:117;;;12230:79;;:::i;:::-;12194:117;12344:4;12336:6;12332:17;12320:29;;12398:3;12390:4;12382:6;12378:17;12368:8;12364:32;12361:41;12358:128;;;12405:79;;:::i;:::-;12358:128;11912:580;;;;;:::o;12515:568::-;12588:8;12598:6;12648:3;12641:4;12633:6;12629:17;12625:27;12615:122;;12656:79;;:::i;:::-;12615:122;12769:6;12756:20;12746:30;;12799:18;12791:6;12788:30;12785:117;;;12821:79;;:::i;:::-;12785:117;12935:4;12927:6;12923:17;12911:29;;12989:3;12981:4;12973:6;12969:17;12959:8;12955:32;12952:41;12949:128;;;12996:79;;:::i;:::-;12949:128;12515:568;;;;;:::o;13105:567::-;13177:8;13187:6;13237:3;13230:4;13222:6;13218:17;13214:27;13204:122;;13245:79;;:::i;:::-;13204:122;13358:6;13345:20;13335:30;;13388:18;13380:6;13377:30;13374:117;;;13410:79;;:::i;:::-;13374:117;13524:4;13516:6;13512:17;13500:29;;13578:3;13570:4;13562:6;13558:17;13548:8;13544:32;13541:41;13538:128;;;13585:79;;:::i;:::-;13538:128;13105:567;;;;;:::o;13678:2131::-;13943:6;13951;13959;13967;13975;13983;13991;13999;14007;14015;14064:3;14052:9;14043:7;14039:23;14035:33;14032:120;;;14071:79;;:::i;:::-;14032:120;14219:1;14208:9;14204:17;14191:31;14249:18;14241:6;14238:30;14235:117;;;14271:79;;:::i;:::-;14235:117;14384:92;14468:7;14459:6;14448:9;14444:22;14384:92;:::i;:::-;14366:110;;;;14162:324;14553:2;14542:9;14538:18;14525:32;14584:18;14576:6;14573:30;14570:117;;;14606:79;;:::i;:::-;14570:117;14719:92;14803:7;14794:6;14783:9;14779:22;14719:92;:::i;:::-;14701:110;;;;14496:325;14888:2;14877:9;14873:18;14860:32;14919:18;14911:6;14908:30;14905:117;;;14941:79;;:::i;:::-;14905:117;15054:80;15126:7;15117:6;15106:9;15102:22;15054:80;:::i;:::-;15036:98;;;;14831:313;15211:2;15200:9;15196:18;15183:32;15242:18;15234:6;15231:30;15228:117;;;15264:79;;:::i;:::-;15228:117;15377:79;15448:7;15439:6;15428:9;15424:22;15377:79;:::i;:::-;15359:97;;;;15154:312;15533:3;15522:9;15518:19;15505:33;15565:18;15557:6;15554:30;15551:117;;;15587:79;;:::i;:::-;15551:117;15700:92;15784:7;15775:6;15764:9;15760:22;15700:92;:::i;:::-;15682:110;;;;15476:326;13678:2131;;;;;;;;;;;;;:::o;15815:114::-;15882:6;15916:5;15910:12;15900:22;;15815:114;;;:::o;15935:184::-;16034:11;16068:6;16063:3;16056:19;16108:4;16103:3;16099:14;16084:29;;15935:184;;;;:::o;16125:132::-;16192:4;16215:3;16207:11;;16245:4;16240:3;16236:14;16228:22;;16125:132;;;:::o;16263:108::-;16340:24;16358:5;16340:24;:::i;:::-;16335:3;16328:37;16263:108;;:::o;16377:179::-;16446:10;16467:46;16509:3;16501:6;16467:46;:::i;:::-;16545:4;16540:3;16536:14;16522:28;;16377:179;;;;:::o;16562:113::-;16632:4;16664;16659:3;16655:14;16647:22;;16562:113;;;:::o;16711:732::-;16830:3;16859:54;16907:5;16859:54;:::i;:::-;16929:86;17008:6;17003:3;16929:86;:::i;:::-;16922:93;;17039:56;17089:5;17039:56;:::i;:::-;17118:7;17149:1;17134:284;17159:6;17156:1;17153:13;17134:284;;;17235:6;17229:13;17262:63;17321:3;17306:13;17262:63;:::i;:::-;17255:70;;17348:60;17401:6;17348:60;:::i;:::-;17338:70;;17194:224;17181:1;17178;17174:9;17169:14;;17134:284;;;17138:14;17434:3;17427:10;;16835:608;;;16711:732;;;;:::o;17449:373::-;17592:4;17630:2;17619:9;17615:18;17607:26;;17679:9;17673:4;17669:20;17665:1;17654:9;17650:17;17643:47;17707:108;17810:4;17801:6;17707:108;:::i;:::-;17699:116;;17449:373;;;;:::o;17828:116::-;17918:1;17911:5;17908:12;17898:40;;17934:1;17931;17924:12;17898:40;17828:116;:::o;17950:173::-;18013:5;18051:6;18038:20;18029:29;;18067:50;18111:5;18067:50;:::i;:::-;17950:173;;;;:::o;18129:851::-;18234:6;18242;18250;18258;18307:2;18295:9;18286:7;18282:23;18278:32;18275:119;;;18313:79;;:::i;:::-;18275:119;18433:1;18458:53;18503:7;18494:6;18483:9;18479:22;18458:53;:::i;:::-;18448:63;;18404:117;18560:2;18586:70;18648:7;18639:6;18628:9;18624:22;18586:70;:::i;:::-;18576:80;;18531:135;18733:2;18722:9;18718:18;18705:32;18764:18;18756:6;18753:30;18750:117;;;18786:79;;:::i;:::-;18750:117;18899:64;18955:7;18946:6;18935:9;18931:22;18899:64;:::i;:::-;18881:82;;;;18676:297;18129:851;;;;;;;:::o;18986:180::-;19034:77;19031:1;19024:88;19131:4;19128:1;19121:15;19155:4;19152:1;19145:15;19172:122;19262:1;19255:5;19252:12;19242:46;;19268:18;;:::i;:::-;19242:46;19172:122;:::o;19300:145::-;19354:7;19383:5;19372:16;;19389:50;19433:5;19389:50;:::i;:::-;19300:145;;;:::o;19451:::-;19516:9;19549:41;19584:5;19549:41;:::i;:::-;19536:54;;19451:145;;;:::o;19602:151::-;19694:52;19740:5;19694:52;:::i;:::-;19689:3;19682:65;19602:151;;:::o;19759:98::-;19810:6;19844:5;19838:12;19828:22;;19759:98;;;:::o;19863:158::-;19936:11;19970:6;19965:3;19958:19;20010:4;20005:3;20001:14;19986:29;;19863:158;;;;:::o;20027:353::-;20103:3;20131:38;20163:5;20131:38;:::i;:::-;20185:60;20238:6;20233:3;20185:60;:::i;:::-;20178:67;;20254:65;20312:6;20307:3;20300:4;20293:5;20289:16;20254:65;:::i;:::-;20344:29;20366:6;20344:29;:::i;:::-;20339:3;20335:39;20328:46;;20107:273;20027:353;;;;:::o;20486:1354::-;20615:3;20651:4;20646:3;20642:14;20744:4;20737:5;20733:16;20727:23;20763:63;20820:4;20815:3;20811:14;20797:12;20763:63;:::i;:::-;20666:170;20927:4;20920:5;20916:16;20910:23;20946:61;21001:4;20996:3;20992:14;20978:12;20946:61;:::i;:::-;20846:171;21107:4;21100:5;21096:16;21090:23;21126:61;21181:4;21176:3;21172:14;21158:12;21126:61;:::i;:::-;21027:170;21286:4;21279:5;21275:16;21269:23;21305:78;21377:4;21372:3;21368:14;21354:12;21305:78;:::i;:::-;21207:186;21477:4;21470:5;21466:16;21460:23;21496:57;21547:4;21542:3;21538:14;21524:12;21496:57;:::i;:::-;21403:160;21652:4;21645:5;21641:16;21635:23;21705:3;21699:4;21695:14;21688:4;21683:3;21679:14;21672:38;21731:71;21797:4;21783:12;21731:71;:::i;:::-;21723:79;;21573:240;21830:4;21823:11;;20620:1220;20486:1354;;;;:::o;21846:393::-;21999:4;22037:2;22026:9;22022:18;22014:26;;22086:9;22080:4;22076:20;22072:1;22061:9;22057:17;22050:47;22114:118;22227:4;22218:6;22114:118;:::i;:::-;22106:126;;21846:393;;;;:::o;22245:161::-;22347:52;22393:5;22347:52;:::i;:::-;22342:3;22335:65;22245:161;;:::o;22412:168::-;22495:11;22529:6;22524:3;22517:19;22569:4;22564:3;22560:14;22545:29;;22412:168;;;;:::o;22586:373::-;22672:3;22700:38;22732:5;22700:38;:::i;:::-;22754:70;22817:6;22812:3;22754:70;:::i;:::-;22747:77;;22833:65;22891:6;22886:3;22879:4;22872:5;22868:16;22833:65;:::i;:::-;22923:29;22945:6;22923:29;:::i;:::-;22918:3;22914:39;22907:46;;22676:283;22586:373;;;;:::o;22965:872::-;23221:4;23259:3;23248:9;23244:19;23236:27;;23273:71;23341:1;23330:9;23326:17;23317:6;23273:71;:::i;:::-;23354:70;23420:2;23409:9;23405:18;23396:6;23354:70;:::i;:::-;23434;23500:2;23489:9;23485:18;23476:6;23434:70;:::i;:::-;23514:87;23597:2;23586:9;23582:18;23573:6;23514:87;:::i;:::-;23611:67;23673:3;23662:9;23658:19;23649:6;23611:67;:::i;:::-;23726:9;23720:4;23716:20;23710:3;23699:9;23695:19;23688:49;23754:76;23825:4;23816:6;23754:76;:::i;:::-;23746:84;;22965:872;;;;;;;;;:::o;23843:116::-;23913:21;23928:5;23913:21;:::i;:::-;23906:5;23903:32;23893:60;;23949:1;23946;23939:12;23893:60;23843:116;:::o;23965:133::-;24008:5;24046:6;24033:20;24024:29;;24062:30;24086:5;24062:30;:::i;:::-;23965:133;;;;:::o;24104:120::-;24176:23;24193:5;24176:23;:::i;:::-;24169:5;24166:34;24156:62;;24214:1;24211;24204:12;24156:62;24104:120;:::o;24230:137::-;24275:5;24313:6;24300:20;24291:29;;24329:32;24355:5;24329:32;:::i;:::-;24230:137;;;;:::o;24387:553::-;24445:8;24455:6;24505:3;24498:4;24490:6;24486:17;24482:27;24472:122;;24513:79;;:::i;:::-;24472:122;24626:6;24613:20;24603:30;;24656:18;24648:6;24645:30;24642:117;;;24678:79;;:::i;:::-;24642:117;24792:4;24784:6;24780:17;24768:29;;24846:3;24838:4;24830:6;24826:17;24816:8;24812:32;24809:41;24806:128;;;24853:79;;:::i;:::-;24806:128;24387:553;;;;;:::o;24946:1447::-;25069:6;25077;25085;25093;25101;25109;25117;25125;25174:3;25162:9;25153:7;25149:23;25145:33;25142:120;;;25181:79;;:::i;:::-;25142:120;25301:1;25326:53;25371:7;25362:6;25351:9;25347:22;25326:53;:::i;:::-;25316:63;;25272:117;25428:2;25454:53;25499:7;25490:6;25479:9;25475:22;25454:53;:::i;:::-;25444:63;;25399:118;25556:2;25582:50;25624:7;25615:6;25604:9;25600:22;25582:50;:::i;:::-;25572:60;;25527:115;25709:2;25698:9;25694:18;25681:32;25740:18;25732:6;25729:30;25726:117;;;25762:79;;:::i;:::-;25726:117;25875:64;25931:7;25922:6;25911:9;25907:22;25875:64;:::i;:::-;25857:82;;;;25652:297;25988:3;26015:52;26059:7;26050:6;26039:9;26035:22;26015:52;:::i;:::-;26005:62;;25959:118;26144:3;26133:9;26129:19;26116:33;26176:18;26168:6;26165:30;26162:117;;;26198:79;;:::i;:::-;26162:117;26311:65;26368:7;26359:6;26348:9;26344:22;26311:65;:::i;:::-;26293:83;;;;26087:299;24946:1447;;;;;;;;;;;:::o;26399:120::-;26471:23;26488:5;26471:23;:::i;:::-;26464:5;26461:34;26451:62;;26509:1;26506;26499:12;26451:62;26399:120;:::o;26525:137::-;26570:5;26608:6;26595:20;26586:29;;26624:32;26650:5;26624:32;:::i;:::-;26525:137;;;;:::o;26668:1509::-;26798:6;26806;26814;26822;26830;26838;26846;26854;26903:3;26891:9;26882:7;26878:23;26874:33;26871:120;;;26910:79;;:::i;:::-;26871:120;27058:1;27047:9;27043:17;27030:31;27088:18;27080:6;27077:30;27074:117;;;27110:79;;:::i;:::-;27074:117;27223:65;27280:7;27271:6;27260:9;27256:22;27223:65;:::i;:::-;27205:83;;;;27001:297;27365:2;27354:9;27350:18;27337:32;27396:18;27388:6;27385:30;27382:117;;;27418:79;;:::i;:::-;27382:117;27531:65;27588:7;27579:6;27568:9;27564:22;27531:65;:::i;:::-;27513:83;;;;27308:298;27645:2;27671:53;27716:7;27707:6;27696:9;27692:22;27671:53;:::i;:::-;27661:63;;27616:118;27773:2;27799:52;27843:7;27834:6;27823:9;27819:22;27799:52;:::i;:::-;27789:62;;27744:117;27928:3;27917:9;27913:19;27900:33;27960:18;27952:6;27949:30;27946:117;;;27982:79;;:::i;:::-;27946:117;28095:65;28152:7;28143:6;28132:9;28128:22;28095:65;:::i;:::-;28077:83;;;;27871:299;26668:1509;;;;;;;;;;;:::o;28183:118::-;28270:24;28288:5;28270:24;:::i;:::-;28265:3;28258:37;28183:118;;:::o;28307:115::-;28392:23;28409:5;28392:23;:::i;:::-;28387:3;28380:36;28307:115;;:::o;28428:169::-;28512:11;28546:6;28541:3;28534:19;28586:4;28581:3;28577:14;28562:29;;28428:169;;;;:::o;28603:377::-;28691:3;28719:39;28752:5;28719:39;:::i;:::-;28774:71;28838:6;28833:3;28774:71;:::i;:::-;28767:78;;28854:65;28912:6;28907:3;28900:4;28893:5;28889:16;28854:65;:::i;:::-;28944:29;28966:6;28944:29;:::i;:::-;28939:3;28935:39;28928:46;;28695:285;28603:377;;;;:::o;28986:1357::-;29351:4;29389:3;29378:9;29374:19;29366:27;;29403:71;29471:1;29460:9;29456:17;29447:6;29403:71;:::i;:::-;29484:72;29552:2;29541:9;29537:18;29528:6;29484:72;:::i;:::-;29566:70;29632:2;29621:9;29617:18;29608:6;29566:70;:::i;:::-;29646;29712:2;29701:9;29697:18;29688:6;29646:70;:::i;:::-;29726:71;29792:3;29781:9;29777:19;29768:6;29726:71;:::i;:::-;29807:67;29869:3;29858:9;29854:19;29845:6;29807:67;:::i;:::-;29922:9;29916:4;29912:20;29906:3;29895:9;29891:19;29884:49;29950:78;30023:4;30014:6;29950:78;:::i;:::-;29942:86;;30076:9;30070:4;30066:20;30060:3;30049:9;30045:19;30038:49;30104:78;30177:4;30168:6;30104:78;:::i;:::-;30096:86;;30230:9;30224:4;30220:20;30214:3;30203:9;30199:19;30192:49;30258:78;30331:4;30322:6;30258:78;:::i;:::-;30250:86;;28986:1357;;;;;;;;;;;;:::o;30349:180::-;30397:77;30394:1;30387:88;30494:4;30491:1;30484:15;30518:4;30515:1;30508:15;30535:320;30579:6;30616:1;30610:4;30606:12;30596:22;;30663:1;30657:4;30653:12;30684:18;30674:81;;30740:4;30732:6;30728:17;30718:27;;30674:81;30802:2;30794:6;30791:14;30771:18;30768:38;30765:84;;30821:18;;:::i;:::-;30765:84;30586:269;30535:320;;;:::o;30861:171::-;31001:23;30997:1;30989:6;30985:14;30978:47;30861:171;:::o;31038:366::-;31180:3;31201:67;31265:2;31260:3;31201:67;:::i;:::-;31194:74;;31277:93;31366:3;31277:93;:::i;:::-;31395:2;31390:3;31386:12;31379:19;;31038:366;;;:::o;31410:419::-;31576:4;31614:2;31603:9;31599:18;31591:26;;31663:9;31657:4;31653:20;31649:1;31638:9;31634:17;31627:47;31691:131;31817:4;31691:131;:::i;:::-;31683:139;;31410:419;;;:::o;31835:180::-;31883:77;31880:1;31873:88;31980:4;31977:1;31970:15;32004:4;32001:1;31994:15;32021:180;32069:77;32066:1;32059:88;32166:4;32163:1;32156:15;32190:4;32187:1;32180:15;32207:327;32265:6;32314:2;32302:9;32293:7;32289:23;32285:32;32282:119;;;32320:79;;:::i;:::-;32282:119;32440:1;32465:52;32509:7;32500:6;32489:9;32485:22;32465:52;:::i;:::-;32455:62;;32411:116;32207:327;;;;:::o;32540:180::-;32588:77;32585:1;32578:88;32685:4;32682:1;32675:15;32709:4;32706:1;32699:15;32726:233;32765:3;32788:24;32806:5;32788:24;:::i;:::-;32779:33;;32834:66;32827:5;32824:77;32821:103;;32904:18;;:::i;:::-;32821:103;32951:1;32944:5;32940:13;32933:20;;32726:233;;;:::o;32965:117::-;33074:1;33071;33064:12;33088:117;33197:1;33194;33187:12;33211:117;33320:1;33317;33310:12;33334:725;33412:4;33418:6;33474:11;33461:25;33574:1;33568:4;33564:12;33553:8;33537:14;33533:29;33529:48;33509:18;33505:73;33495:168;;33582:79;;:::i;:::-;33495:168;33694:18;33684:8;33680:33;33672:41;;33746:4;33733:18;33723:28;;33774:18;33766:6;33763:30;33760:117;;;33796:79;;:::i;:::-;33760:117;33904:2;33898:4;33894:13;33886:21;;33961:4;33953:6;33949:17;33933:14;33929:38;33923:4;33919:49;33916:136;;;33971:79;;:::i;:::-;33916:136;33425:634;33334:725;;;;;:::o;34065:97::-;34124:6;34152:3;34142:13;;34065:97;;;;:::o;34168:141::-;34217:4;34240:3;34232:11;;34263:3;34260:1;34253:14;34297:4;34294:1;34284:18;34276:26;;34168:141;;;:::o;34315:93::-;34352:6;34399:2;34394;34387:5;34383:14;34379:23;34369:33;;34315:93;;;:::o;34414:107::-;34458:8;34508:5;34502:4;34498:16;34477:37;;34414:107;;;;:::o;34527:393::-;34596:6;34646:1;34634:10;34630:18;34669:97;34699:66;34688:9;34669:97;:::i;:::-;34787:39;34817:8;34806:9;34787:39;:::i;:::-;34775:51;;34859:4;34855:9;34848:5;34844:21;34835:30;;34908:4;34898:8;34894:19;34887:5;34884:30;34874:40;;34603:317;;34527:393;;;;;:::o;34926:60::-;34954:3;34975:5;34968:12;;34926:60;;;:::o;34992:142::-;35042:9;35075:53;35093:34;35102:24;35120:5;35102:24;:::i;:::-;35093:34;:::i;:::-;35075:53;:::i;:::-;35062:66;;34992:142;;;:::o;35140:75::-;35183:3;35204:5;35197:12;;35140:75;;;:::o;35221:269::-;35331:39;35362:7;35331:39;:::i;:::-;35392:91;35441:41;35465:16;35441:41;:::i;:::-;35433:6;35426:4;35420:11;35392:91;:::i;:::-;35386:4;35379:105;35297:193;35221:269;;;:::o;35496:73::-;35541:3;35562:1;35555:8;;35496:73;:::o;35575:189::-;35652:32;;:::i;:::-;35693:65;35751:6;35743;35737:4;35693:65;:::i;:::-;35628:136;35575:189;;:::o;35770:186::-;35830:120;35847:3;35840:5;35837:14;35830:120;;;35901:39;35938:1;35931:5;35901:39;:::i;:::-;35874:1;35867:5;35863:13;35854:22;;35830:120;;;35770:186;;:::o;35962:543::-;36063:2;36058:3;36055:11;36052:446;;;36097:38;36129:5;36097:38;:::i;:::-;36181:29;36199:10;36181:29;:::i;:::-;36171:8;36167:44;36364:2;36352:10;36349:18;36346:49;;;36385:8;36370:23;;36346:49;36408:80;36464:22;36482:3;36464:22;:::i;:::-;36454:8;36450:37;36437:11;36408:80;:::i;:::-;36067:431;;36052:446;35962:543;;;:::o;36511:117::-;36565:8;36615:5;36609:4;36605:16;36584:37;;36511:117;;;;:::o;36634:169::-;36678:6;36711:51;36759:1;36755:6;36747:5;36744:1;36740:13;36711:51;:::i;:::-;36707:56;36792:4;36786;36782:15;36772:25;;36685:118;36634:169;;;;:::o;36808:295::-;36884:4;37030:29;37055:3;37049:4;37030:29;:::i;:::-;37022:37;;37092:3;37089:1;37085:11;37079:4;37076:21;37068:29;;36808:295;;;;:::o;37108:1403::-;37232:44;37272:3;37267;37232:44;:::i;:::-;37341:18;37333:6;37330:30;37327:56;;;37363:18;;:::i;:::-;37327:56;37407:38;37439:4;37433:11;37407:38;:::i;:::-;37492:67;37552:6;37544;37538:4;37492:67;:::i;:::-;37586:1;37615:2;37607:6;37604:14;37632:1;37627:632;;;;38303:1;38320:6;38317:84;;;38376:9;38371:3;38367:19;38354:33;38345:42;;38317:84;38427:67;38487:6;38480:5;38427:67;:::i;:::-;38421:4;38414:81;38276:229;37597:908;;37627:632;37679:4;37675:9;37667:6;37663:22;37713:37;37745:4;37713:37;:::i;:::-;37772:1;37786:215;37800:7;37797:1;37794:14;37786:215;;;37886:9;37881:3;37877:19;37864:33;37856:6;37849:49;37937:1;37929:6;37925:14;37915:24;;37984:2;37973:9;37969:18;37956:31;;37823:4;37820:1;37816:12;37811:17;;37786:215;;;38029:6;38020:7;38017:19;38014:186;;;38094:9;38089:3;38085:19;38072:33;38137:48;38179:4;38171:6;38167:17;38156:9;38137:48;:::i;:::-;38129:6;38122:64;38037:163;38014:186;38246:1;38242;38234:6;38230:14;38226:22;38220:4;38213:36;37634:625;;;37597:908;;37207:1304;;;37108:1403;;;:::o;38517:96::-;38575:6;38603:3;38593:13;;38517:96;;;;:::o;38619:140::-;38667:4;38690:3;38682:11;;38713:3;38710:1;38703:14;38747:4;38744:1;38734:18;38726:26;;38619:140;;;:::o;38765:541::-;38865:2;38860:3;38857:11;38854:445;;;38899:37;38930:5;38899:37;:::i;:::-;38982:29;39000:10;38982:29;:::i;:::-;38972:8;38968:44;39165:2;39153:10;39150:18;39147:49;;;39186:8;39171:23;;39147:49;39209:80;39265:22;39283:3;39265:22;:::i;:::-;39255:8;39251:37;39238:11;39209:80;:::i;:::-;38869:430;;38854:445;38765:541;;;:::o;39312:1398::-;39434:43;39473:3;39468;39434:43;:::i;:::-;39542:18;39534:6;39531:30;39528:56;;;39564:18;;:::i;:::-;39528:56;39608:38;39640:4;39634:11;39608:38;:::i;:::-;39693:66;39752:6;39744;39738:4;39693:66;:::i;:::-;39786:1;39815:2;39807:6;39804:14;39832:1;39827:631;;;;40502:1;40519:6;40516:84;;;40575:9;40570:3;40566:19;40553:33;40544:42;;40516:84;40626:67;40686:6;40679:5;40626:67;:::i;:::-;40620:4;40613:81;40475:229;39797:907;;39827:631;39879:4;39875:9;39867:6;39863:22;39913:36;39944:4;39913:36;:::i;:::-;39971:1;39985:215;39999:7;39996:1;39993:14;39985:215;;;40085:9;40080:3;40076:19;40063:33;40055:6;40048:49;40136:1;40128:6;40124:14;40114:24;;40183:2;40172:9;40168:18;40155:31;;40022:4;40019:1;40015:12;40010:17;;39985:215;;;40228:6;40219:7;40216:19;40213:186;;;40293:9;40288:3;40284:19;40271:33;40336:48;40378:4;40370:6;40366:17;40355:9;40336:48;:::i;:::-;40328:6;40321:64;40236:163;40213:186;40445:1;40441;40433:6;40429:14;40425:22;40419:4;40412:36;39834:624;;;39797:907;;39409:1301;;;39312:1398;;;:::o;40716:332::-;40837:4;40875:2;40864:9;40860:18;40852:26;;40888:71;40956:1;40945:9;40941:17;40932:6;40888:71;:::i;:::-;40969:72;41037:2;41026:9;41022:18;41013:6;40969:72;:::i;:::-;40716:332;;;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"ADMIN_ROLE()": "75b238fc", "DEFAULT_ADMIN_ROLE()": "a217fddf", "DEVELOPER_ROLE()": "9103a0e0", "MAX_GAS_LIMIT()": "e3f5aa51", "addTrigger(uint256,uint8,bytes)": "711b0802", "batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])": "5f0710a2", "executionHistory(uint256,uint256)": "00d5c659", "fireTrigger(uint256,bytes)": "53e7768b", "functions(uint256)": "eac0bd90", "getExecutionCount(uint256)": "0250fc7f", "getFunction(uint256)": "0e097de7", "getRoleAdmin(bytes32)": "248a9ca3", "getTrigger(uint256)": "9c50472b", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "isActive(uint256)": "82afd23b", "nextFunctionId()": "02b9db7b", "nextTriggerId()": "42227fa4", "registerFunction(string,string,bytes32,uint96,string)": "b4332d86", "renounceRole(bytes32,address)": "36568abe", "reportExecution(uint256,uint256,bool,bytes,uint32,string)": "aed5a3e1", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7", "triggers(uint256)": "a78dd42e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.30+commit.73712a01\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FunctionInactive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FunctionNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"GasLimitExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidWasmHash\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TriggerInactive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TriggerNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedAccess\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"name\":\"FunctionExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"}],\"name\":\"FunctionRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"TriggerAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"TriggerFired\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEVELOPER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_GAS_LIMIT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"name\":\"addTrigger\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"names\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"descriptions\",\"type\":\"string[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"wasmHashes\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"gasLimits\",\"type\":\"uint96[]\"},{\"internalType\":\"string[]\",\"name\":\"runtimes\",\"type\":\"string[]\"}],\"name\":\"batchRegisterFunctions\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"functionIds\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"executionHistory\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"timestamp\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"gasUsed\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"fireTrigger\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"functions\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"uint64\",\"name\":\"createdAt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"executionCount\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"getExecutionCount\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"getFunction\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"uint64\",\"name\":\"createdAt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"executionCount\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"internalType\":\"struct OptimizedFunctionRegistry.FunctionMetadata\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"}],\"name\":\"getTrigger\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"lastTriggered\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"triggerCount\",\"type\":\"uint64\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"internalType\":\"struct OptimizedFunctionRegistry.TriggerRule\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"isActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nextFunctionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nextTriggerId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"name\":\"registerFunction\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"},{\"internalType\":\"uint32\",\"name\":\"gasUsed\",\"type\":\"uint32\"},{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"reportExecution\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"triggers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"lastTriggered\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"triggerCount\",\"type\":\"uint64\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"MonadBot\",\"details\":\"Gas-optimized Monad FaaS Function Registry\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"addTrigger(uint256,uint8,bytes)\":{\"details\":\"Add a trigger rule (gas optimized)\"},\"batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])\":{\"details\":\"Batch register functions for gas efficiency\"},\"fireTrigger(uint256,bytes)\":{\"details\":\"Fire a trigger (gas optimized)\"},\"getExecutionCount(uint256)\":{\"details\":\"Get execution count only (gas efficient)\"},\"getFunction(uint256)\":{\"details\":\"Get function metadata (view function)\"},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getTrigger(uint256)\":{\"details\":\"Get trigger rule (view function)\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"isActive(uint256)\":{\"details\":\"Check if function is active (gas efficient)\"},\"registerFunction(string,string,bytes32,uint96,string)\":{\"details\":\"Register a new serverless function (gas optimized)\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"reportExecution(uint256,uint256,bool,bytes,uint32,string)\":{\"details\":\"Report function execution (gas optimized)\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"title\":\"OptimizedFunctionRegistry\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/OptimizedFunctionRegistry.sol\":\"OptimizedFunctionRegistry\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67\",\"dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"src/OptimizedFunctionRegistry.sol\":{\"keccak256\":\"0x2a77c792714e62b41f5b80ee7f3a54851e16b5a0305bd78f10fd74365c88e17f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d63ba57861c5e987655d79a6f77342c2797935a754c628d229670a4faf9e3526\",\"dweb:/ipfs/QmURcA1fjMPPVVhvo6p3cma84vxSMZPCNbmGXiessY4tBJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.30+commit.73712a01"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "FunctionInactive"}, {"inputs": [], "type": "error", "name": "FunctionNotFound"}, {"inputs": [], "type": "error", "name": "GasLimitExceeded"}, {"inputs": [], "type": "error", "name": "InvalidWasmHash"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "TriggerInactive"}, {"inputs": [], "type": "error", "name": "TriggerNotFound"}, {"inputs": [], "type": "error", "name": "UnauthorizedAccess"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "bool", "name": "success", "type": "bool", "indexed": false}], "type": "event", "name": "FunctionExecuted", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "bytes32", "name": "wasmHash", "type": "bytes32", "indexed": false}], "type": "event", "name": "FunctionRegistered", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}], "type": "event", "name": "TriggerAdded", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}], "type": "event", "name": "TriggerFired", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEVELOPER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_GAS_LIMIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "addTrigger", "outputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}]}, {"inputs": [{"internalType": "string[]", "name": "names", "type": "string[]"}, {"internalType": "string[]", "name": "descriptions", "type": "string[]"}, {"internalType": "bytes32[]", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes32[]"}, {"internalType": "uint96[]", "name": "gasLimits", "type": "uint96[]"}, {"internalType": "string[]", "name": "runtimes", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchRegisterFunctions", "outputs": [{"internalType": "uint256[]", "name": "functionIds", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "executionHistory", "outputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint32", "name": "gasUsed", "type": "uint32"}, {"internalType": "bool", "name": "success", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "fireTrigger"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "functions", "outputs": [{"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "uint64", "name": "createdAt", "type": "uint64"}, {"internalType": "uint64", "name": "executionCount", "type": "uint64"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "runtime", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getExecutionCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getFunction", "outputs": [{"internalType": "struct OptimizedFunctionRegistry.FunctionMetadata", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "uint64", "name": "createdAt", "type": "uint64"}, {"internalType": "uint64", "name": "executionCount", "type": "uint64"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "runtime", "type": "string"}]}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getTrigger", "outputs": [{"internalType": "struct OptimizedFunctionRegistry.TriggerRule", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint64", "name": "lastTriggered", "type": "uint64"}, {"internalType": "uint64", "name": "triggerCount", "type": "uint64"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "isActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "nextFunctionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "nextTriggerId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "string", "name": "runtime", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "registerFunction", "outputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint256", "name": "triggerId", "type": "uint256"}, {"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}, {"internalType": "uint32", "name": "gasUsed", "type": "uint32"}, {"internalType": "string", "name": "", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "reportExecution"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "triggers", "outputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint64", "name": "lastTriggered", "type": "uint64"}, {"internalType": "uint64", "name": "triggerCount", "type": "uint64"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}]}], "devdoc": {"kind": "dev", "methods": {"addTrigger(uint256,uint8,bytes)": {"details": "Add a trigger rule (gas optimized)"}, "batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])": {"details": "Batch register functions for gas efficiency"}, "fireTrigger(uint256,bytes)": {"details": "Fire a trigger (gas optimized)"}, "getExecutionCount(uint256)": {"details": "Get execution count only (gas efficient)"}, "getFunction(uint256)": {"details": "Get function metadata (view function)"}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getTrigger(uint256)": {"details": "Get trigger rule (view function)"}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "isActive(uint256)": {"details": "Check if function is active (gas efficient)"}, "registerFunction(string,string,bytes32,uint96,string)": {"details": "Register a new serverless function (gas optimized)"}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "reportExecution(uint256,uint256,bool,bytes,uint32,string)": {"details": "Report function execution (gas optimized)"}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/OptimizedFunctionRegistry.sol": "OptimizedFunctionRegistry"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458", "urls": ["bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67", "dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3", "urls": ["bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a", "dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa", "urls": ["bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287", "dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "src/OptimizedFunctionRegistry.sol": {"keccak256": "0x2a77c792714e62b41f5b80ee7f3a54851e16b5a0305bd78f10fd74365c88e17f", "urls": ["bzz-raw://d63ba57861c5e987655d79a6f77342c2797935a754c628d229670a4faf9e3526", "dweb:/ipfs/QmURcA1fjMPPVVhvo6p3cma84vxSMZPCNbmGXiessY4tBJ"], "license": "MIT"}}, "version": 1}, "id": 22}